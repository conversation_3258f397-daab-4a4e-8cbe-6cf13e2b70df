"""
向量数据库增强的Agent
支持从向量数据库检索复杂提示词并执行相应的处理流程
"""

import logging
import re
from typing import Dict, Any, Optional, List
from src.agents.province_aware_agent import ProvinceAwareAgent
from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

logger = logging.getLogger(__name__)


class VectorEnhancedAgent(ProvinceAwareAgent):
    """
    向量数据库增强的Agent
    在处理用户问题前先查询向量数据库，如有匹配则按照存储的流程执行
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.prompt_manager = get_prompt_vectorstore_manager()
        self.vector_search_threshold = 0.95  # 向量搜索相似度阈值（降低以匹配更多查询）
        self.current_complex_prompt = None  # 当前匹配的复杂提示词
        self.original_province_code = None  # 原始省份代码
        self.current_database_override = None  # 当前数据库覆盖
        self.current_user_message = None  # 当前用户消息
    
    def process_message(self, message: str, conversation_history: Optional[List[Dict]] = None,
                       system_message: str = None) -> Dict[str, Any]:
        """
        处理用户消息，支持向量数据库增强

        Args:
            message: 用户消息
            conversation_history: 对话历史
            system_message: 系统消息，用于提取省份代码

        Returns:
            包含回复和工具调用结果的字典
        """
        # 🔍 详细的Agent状态日志
        logger.info(f"🤖 [向量增强Agent] 开始处理用户消息:")
        logger.info(f"  - Agent实例ID: {id(self)}")
        logger.info(f"  - 用户消息: {message}")
        logger.info(f"  - 系统消息: {system_message}")
        logger.info(f"  - 对话历史长度: {len(conversation_history) if conversation_history else 0}")
        logger.info(f"  - 当前状态 - 复杂提示词: {self.current_complex_prompt.title if self.current_complex_prompt else None}")
        logger.info(f"  - 当前状态 - 省份代码: {self.original_province_code}")
        logger.info(f"  - 当前状态 - 数据库覆盖: {self.current_database_override}")
        logger.info(f"  - 当前状态 - 用户消息: {self.current_user_message}")

        print(f"🤖 [Agent-{id(self)}] 处理消息: {message[:50]}...")
        print(f"  - 当前复杂提示词: {self.current_complex_prompt.title if self.current_complex_prompt else 'None'}")
        print(f"  - 当前省份代码: {self.original_province_code}")

        # 保存原始省份代码和用户消息
        if system_message:
            self.original_province_code = self._extract_province_code_from_system(system_message)
            logger.info(f"💾 [数据库切换] 保存原始省份代码: {self.original_province_code}")

        self.current_user_message = message

        # 第一步：提取纯粹的用户问题并查询向量数据库
        user_question = self._extract_user_question(message)
        logger.info(f"🔍 [向量搜索] 提取的用户问题: {user_question}")
        matched_prompts = self._search_vector_database(user_question)

        if matched_prompts:
            # 有匹配结果，使用复杂提示词处理
            best_prompt, similarity = matched_prompts[0]
            logger.info(f"🎯 [向量增强Agent] 找到匹配的复杂提示词:")
            logger.info(f"  - 提示词标题: {best_prompt.title}")
            logger.info(f"  - 相似度: {similarity:.3f}")
            logger.info(f"  - 提示词ID: {best_prompt.id}")
            logger.info(f"  - 触发问题: {best_prompt.trigger_questions}")

            print(f"🎯 [向量匹配] 匹配到复杂提示词: {best_prompt.title} (相似度: {similarity:.3f})")

            self.current_complex_prompt = best_prompt
            
            # 构建增强的系统提示词
            enhanced_system_prompt = self._build_enhanced_system_prompt(best_prompt)
            
            # 临时替换系统提示词
            original_system_prompt = self.system_prompt
            self.system_prompt = enhanced_system_prompt

            # 检查是否需要临时调整LLM的think模式
            original_preserve_think = None
            if hasattr(self.llm, 'preserve_think_tags'):
                original_preserve_think = self.llm.preserve_think_tags
                # 如果复杂提示词启用了极速模式，临时关闭think标签
                if best_prompt.no_think_mode:
                    self.llm.preserve_think_tags = False
                    logger.info(f"🚀 [向量增强Agent] 启用极速模式，关闭think标签")

            try:
                # 使用增强的提示词处理消息
                # 注意：这里传递的是原始的system_message，不是enhanced_system_prompt
                result = super().process_message(message, conversation_history, system_message)

                # 在结果中标记使用了复杂提示词
                result["used_complex_prompt"] = True
                result["complex_prompt_info"] = {
                    "id": best_prompt.id,
                    "title": best_prompt.title,
                    "similarity": similarity
                }

                return result

            finally:
                # 恢复原始系统提示词
                self.system_prompt = original_system_prompt

                # 恢复原始think模式设置
                if original_preserve_think is not None and hasattr(self.llm, 'preserve_think_tags'):
                    self.llm.preserve_think_tags = original_preserve_think

                # 🔍 状态清理日志
                logger.info(f"🧹 [状态清理] Agent状态恢复:")
                logger.info(f"  - 清理前复杂提示词: {self.current_complex_prompt.title if self.current_complex_prompt else None}")
                self.current_complex_prompt = None
                logger.info(f"  - 清理后复杂提示词: {self.current_complex_prompt}")
                print(f"🧹 [Agent-{id(self)}] 状态已清理，复杂提示词已重置")
        
        else:
            # 没有匹配结果，使用默认处理逻辑
            logger.info("🔄 [向量增强Agent] 未找到匹配的复杂提示词，使用默认处理逻辑")
            result = super().process_message(message, conversation_history, system_message)
            result["used_complex_prompt"] = False
            return result
    
    async def process_message_async(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                                  real_time_callback: Optional[callable] = None,
                                  system_message: str = None) -> Dict[str, Any]:
        """
        异步处理用户消息，支持向量数据库增强
        """
        # 🔍 详细的Agent状态日志（异步版本）
        logger.info(f"🤖 [向量增强Agent-异步] 开始处理用户消息:")
        logger.info(f"  - Agent实例ID: {id(self)}")
        logger.info(f"  - 用户消息: {message}")
        logger.info(f"  - 系统消息: {system_message}")
        logger.info(f"  - 对话历史长度: {len(conversation_history) if conversation_history else 0}")
        logger.info(f"  - 当前状态 - 复杂提示词: {self.current_complex_prompt.title if self.current_complex_prompt else None}")
        logger.info(f"  - 当前状态 - 省份代码: {self.original_province_code}")
        logger.info(f"  - 当前状态 - 数据库覆盖: {self.current_database_override}")
        logger.info(f"  - 当前状态 - 用户消息: {self.current_user_message}")

        print(f"🤖 [异步Agent-{id(self)}] 处理消息: {message[:50]}...")
        print(f"  - 当前复杂提示词: {self.current_complex_prompt.title if self.current_complex_prompt else 'None'}")
        print(f"  - 当前省份代码: {self.original_province_code}")

        # 保存原始省份代码和用户消息
        if system_message:
            self.original_province_code = self._extract_province_code_from_system(system_message)
            logger.info(f"💾 [数据库切换] 保存原始省份代码: {self.original_province_code}")

        self.current_user_message = message

        # 第一步：提取纯粹的用户问题并查询向量数据库
        user_question = self._extract_user_question(message)
        logger.info(f"🔍 [向量搜索] 提取的用户问题: {user_question}")
        matched_prompts = self._search_vector_database(user_question)
        
        if matched_prompts:
            # 有匹配结果，使用复杂提示词处理
            best_prompt, similarity = matched_prompts[0]
            logger.info(f"🎯 [向量增强Agent] 找到匹配的复杂提示词: {best_prompt.title} (相似度: {similarity:.3f})")
            
            self.current_complex_prompt = best_prompt
            
            # 构建增强的系统提示词
            enhanced_system_prompt = self._build_enhanced_system_prompt(best_prompt)
            
            # 临时替换系统提示词
            original_system_prompt = self.system_prompt
            self.system_prompt = enhanced_system_prompt

            # 检查是否需要临时调整LLM的think模式
            original_preserve_think = None
            if hasattr(self.llm, 'preserve_think_tags'):
                original_preserve_think = self.llm.preserve_think_tags
                # 如果复杂提示词启用了极速模式，临时关闭think标签
                if best_prompt.no_think_mode:
                    self.llm.preserve_think_tags = False
                    logger.info(f"🚀 [向量增强Agent] 启用极速模式，关闭think标签")

            try:
                # 使用增强的提示词处理消息
                result = await super().process_message_async(message, conversation_history, real_time_callback, system_message)
                
                # 在结果中标记使用了复杂提示词
                result["used_complex_prompt"] = True
                result["complex_prompt_info"] = {
                    "id": best_prompt.id,
                    "title": best_prompt.title,
                    "similarity": similarity
                }
                
                return result
                
            finally:
                # 恢复原始系统提示词
                self.system_prompt = original_system_prompt

                # 恢复原始think模式设置
                if original_preserve_think is not None and hasattr(self.llm, 'preserve_think_tags'):
                    self.llm.preserve_think_tags = original_preserve_think
                self.current_complex_prompt = None
        
        else:
            # 没有匹配结果，使用默认处理逻辑
            logger.info("🔄 [向量增强Agent] 未找到匹配的复杂提示词，使用默认处理逻辑")
            result = await super().process_message_async(message, conversation_history, real_time_callback, system_message)
            result["used_complex_prompt"] = False
            return result
    
    def _search_vector_database(self, query: str) -> List[tuple]:
        """
        搜索向量数据库
        
        Args:
            query: 查询文本
            
        Returns:
            List[tuple]: 匹配的复杂提示词列表
        """
        try:
            logger.info(f"🔍 [向量搜索] 搜索查询: {query}")
            
            # 搜索向量数据库
            matched_prompts = self.prompt_manager.search_complex_prompts(
                query=query,
                k=3,
                score_threshold=self.vector_search_threshold
            )
            
            if matched_prompts:
                logger.info(f"✅ [向量搜索] 找到 {len(matched_prompts)} 个匹配结果")
                for i, (prompt, similarity) in enumerate(matched_prompts, 1):
                    logger.info(f"  [{i}] {prompt.title} (相似度: {similarity:.3f}, 优先级: {prompt.priority})")
            else:
                logger.info("❌ [向量搜索] 未找到匹配的复杂提示词")
            
            return matched_prompts
            
        except Exception as e:
            logger.error(f"❌ [向量搜索] 搜索失败: {e}")
            return []

    def _extract_user_question(self, message: str) -> str:
        """
        从完整消息中提取纯粹的用户问题

        Args:
            message: 完整的消息内容，可能包含"数据库:XX\n\n用户问题: 具体问题"格式

        Returns:
            str: 提取的用户问题
        """
        if not message:
            return ""

        # 查找"用户问题:"后面的内容
        pattern = r'用户问题\s*[:：]\s*(.+?)(?:\n|$)'
        match = re.search(pattern, message, re.DOTALL)
        if match:
            user_question = match.group(1).strip()
            logger.info(f"🎯 [问题提取] 从消息中提取到用户问题: {user_question}")
            return user_question

        # 如果没有找到"用户问题:"格式，检查是否包含"数据库:"
        if "数据库:" in message:
            # 尝试提取数据库信息后的内容
            lines = message.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith('数据库:'):
                    # 获取数据库行之后的所有内容
                    remaining_lines = lines[i+1:]
                    # 过滤空行
                    non_empty_lines = [line.strip() for line in remaining_lines if line.strip()]
                    if non_empty_lines:
                        user_question = '\n'.join(non_empty_lines)
                        logger.info(f"🎯 [问题提取] 从数据库信息后提取到用户问题: {user_question}")
                        return user_question

        # 如果都没有匹配，返回原始消息
        logger.info(f"🎯 [问题提取] 未找到特定格式，使用原始消息: {message}")
        return message

    def _get_default_system_prompt(self) -> str:
        """
        获取默认系统提示词
        """
        # 如果有原始系统提示词，使用它
        if hasattr(self, 'system_prompt') and self.system_prompt:
            return self.system_prompt

        # 否则从父类获取默认提示词
        try:
            from examples.analysis_agent_server import _get_optimized_system_prompt
            return _get_optimized_system_prompt()
        except ImportError:
            # 如果无法导入，返回基础提示词
            return """
你是一个专业的数据分析助手，擅长使用工具查询和分析数据。

## 🛠️ 可用工具
- integrated_sql: 查询数据库获取数据
- knowledge_search: 搜索知识库获取专业建议

## 🔧 工具调用规则
- 根据用户需求调用相应工具
- 可以调用多个工具来完成复杂任务
- 必须基于工具返回的数据给出回答
"""
    

    
    def get_vector_search_stats(self) -> Dict[str, Any]:
        """获取向量搜索统计信息"""
        try:
            all_prompts = self.prompt_manager.get_all_prompts()
            return {
                "total_prompts": len(all_prompts),
                "search_threshold": self.vector_search_threshold,
                "current_prompt": {
                    "id": self.current_complex_prompt.id,
                    "title": self.current_complex_prompt.title
                } if self.current_complex_prompt else None
            }
        except Exception as e:
            logger.error(f"❌ 获取向量搜索统计失败: {e}")
            return {"error": str(e)}

    def _parse_standardized_commands(self, processing_steps: List[str]) -> List[str]:
        """
        解析处理步骤中的标准化命令

        支持的语法：
        - [SQL查询:JT:当前电费情况如何] → 切换到JT数据库并执行SQL查询
        - [切换数据库:原始] → 只切换数据库配置，不执行查询
        - [调用工具:knowledge_search:劣化指标改进建议] → 调用指定工具
        - [计算:复杂计算逻辑] → 执行计算逻辑
        - [变量:结果=查询结果] → 设置变量

        Args:
            processing_steps: 原始处理步骤列表

        Returns:
            处理后的步骤列表，标准化命令会被转换为相应的指令
        """
        enhanced_steps = []

        for step in processing_steps:
            # 解析各种标准化命令
            enhanced_step = self._parse_single_step(step)
            enhanced_steps.extend(enhanced_step)

        return enhanced_steps

    def _parse_single_step(self, step: str) -> List[str]:
        """
        解析单个步骤中的标准化命令

        Args:
            step: 单个处理步骤

        Returns:
            解析后的步骤列表
        """
        result_steps = []

        # 1. SQL查询命令: [SQL查询:数据库:查询内容]
        sql_pattern = r'\[SQL查询[:：]([^:：\]]+)[:：]([^\]]+)\]'
        sql_match = re.search(sql_pattern, step)
        if sql_match:
            target_db = sql_match.group(1).strip()
            query_content = sql_match.group(2).strip()

            # 应用动态参数替换
            target_db = self._replace_dynamic_parameters(target_db)
            query_content = self._replace_dynamic_parameters(query_content)

            logger.info(f"🔍 [SQL查询] 检测到SQL查询命令: 数据库={target_db}, 查询={query_content}")

            # 直接生成包含target_database的SQL查询指令
            sql_direct_step = f"[内部指令-SQL-直接] 数据库:{target_db} | 查询:{query_content}"
            result_steps.append(sql_direct_step)
            logger.info(f"🔧 [SQL解析] 生成直接SQL指令: {sql_direct_step}")

            # 移除命令，保留其余内容
            clean_step = re.sub(sql_pattern, '', step).strip()
            if clean_step:
                # 对剩余内容也应用参数替换
                clean_step = self._replace_dynamic_parameters(clean_step)
                result_steps.append(clean_step)

            return result_steps

        # 2. 数据库切换命令: [切换数据库:数据库]
        switch_pattern = r'\[切换数据库[:：]([^\]]+)\]'
        switch_match = re.search(switch_pattern, step)
        if switch_match:
            target_db = switch_match.group(1).strip()

            logger.info(f"🔄 [数据库切换] 检测到数据库切换命令: {target_db}")

            # 根据目标数据库生成相应的指令
            if target_db.upper() == "原始" or target_db.upper() == "ORIGINAL":
                if self.original_province_code:
                    result_steps.append(f"[内部指令-切换] 切换到原始数据库: {self.original_province_code}")
                    self.current_database_override = None
                else:
                    result_steps.append("[内部指令-切换] 切换到默认数据库")
            else:
                result_steps.append(f"[内部指令-切换] 切换到数据库: {target_db}")
                self.current_database_override = target_db

            # 移除命令，保留其余内容
            clean_step = re.sub(switch_pattern, '', step).strip()
            if clean_step:
                result_steps.append(clean_step)

            return result_steps

        # 3. 工具调用命令: [调用工具:工具名:参数]
        tool_pattern = r'\[调用工具[:：]([^:：\]]+)[:：]([^\]]+)\]'
        tool_match = re.search(tool_pattern, step)
        if tool_match:
            tool_name = tool_match.group(1).strip()
            tool_params = tool_match.group(2).strip()

            # 应用动态参数替换
            tool_params = self._replace_dynamic_parameters(tool_params)

            # 工具名称映射（支持简洁别名）
            actual_tool_name = self._map_tool_name(tool_name)

            logger.info(f"🔧 [工具调用] 检测到工具调用命令: 工具={tool_name} → {actual_tool_name}, 参数={tool_params}")

            result_steps.append(f"[内部指令-工具] 调用{actual_tool_name}工具，参数: {tool_params}")

            # 移除命令，保留其余内容
            clean_step = re.sub(tool_pattern, '', step).strip()
            if clean_step:
                clean_step = self._replace_dynamic_parameters(clean_step)
                result_steps.append(clean_step)

            return result_steps

        # 4. 计算命令: [计算:计算逻辑]
        calc_pattern = r'\[计算[:：]([^\]]+)\]'
        calc_match = re.search(calc_pattern, step)
        if calc_match:
            calc_logic = calc_match.group(1).strip()

            # 应用动态参数替换
            calc_logic = self._replace_dynamic_parameters(calc_logic)

            logger.info(f"🧮 [计算] 检测到计算命令: {calc_logic}")

            result_steps.append(f"[内部指令-计算] 执行计算: {calc_logic}")

            # 移除命令，保留其余内容
            clean_step = re.sub(calc_pattern, '', step).strip()
            if clean_step:
                clean_step = self._replace_dynamic_parameters(clean_step)
                result_steps.append(clean_step)

            return result_steps

        # 5. 变量设置命令: [变量:变量名=值]
        var_pattern = r'\[变量[:：]([^\]]+)\]'
        var_match = re.search(var_pattern, step)
        if var_match:
            var_definition = var_match.group(1).strip()

            logger.info(f"📝 [变量] 检测到变量设置命令: {var_definition}")

            result_steps.append(f"[内部指令-变量] 设置变量: {var_definition}")

            # 移除命令，保留其余内容
            clean_step = re.sub(var_pattern, '', step).strip()
            if clean_step:
                result_steps.append(clean_step)

            return result_steps

        # 6. 普通步骤，直接添加
        result_steps.append(step)
        return result_steps



    def _build_enhanced_system_prompt(self, complex_prompt: ComplexPrompt) -> str:
        """
        构建增强的系统提示词，包含处理步骤和回复格式要求
        """
        # 合并处理步骤
        steps_prompt = "\n\n".join(complex_prompt.processing_steps)

        # 添加工具调用说明
        tool_instruction = """
## 🔧 工具调用说明
当需要调用工具时，必须使用以下JSON格式：

**调用integrated_sql工具：**
```json
{"tool_name": "integrated_sql", "parameters": {"question": "你的查询问题", "target_database": "JT"}}
```

**调用knowledge_search工具：**
```json
{"tool_name": "knowledge_search", "parameters": {"query": "你的搜索内容"}}
```

⚠️ 重要：必须使用JSON格式调用工具，不能直接给出答案！"""

        # 构建完整的提示词
        if complex_prompt.response_format and complex_prompt.response_format.strip():
            # 如果有回复格式要求，添加到提示词中
            full_prompt = f"""{steps_prompt}

{tool_instruction}

## 📋 回复格式要求
请严格按照以下格式输出结果：

{complex_prompt.response_format}

⚠️ 重要：必须严格按照上述格式输出，不能省略任何部分！"""
        else:
            # 如果没有回复格式要求，只使用处理步骤和工具说明
            full_prompt = f"""{steps_prompt}

{tool_instruction}"""

        # 进行参数替换
        enhanced_prompt = self._replace_dynamic_parameters(full_prompt)

        return enhanced_prompt



    def _modify_system_message_for_database_switch(self, original_system: str, target_database: str) -> str:
        """
        修改system消息以支持数据库切换

        Args:
            original_system: 原始system消息
            target_database: 目标数据库代码

        Returns:
            修改后的system消息
        """
        if not original_system:
            return f"数据库:{target_database}"

        # 替换现有的数据库配置
        pattern = r'数据库\s*[:：]\s*[A-Z]{1,3}'
        if re.search(pattern, original_system):
            modified_system = re.sub(pattern, f'数据库:{target_database}', original_system)
        else:
            # 如果没有数据库配置，添加一个
            modified_system = f"{original_system} 数据库:{target_database}"

        logger.info(f"🔄 [数据库切换] 修改system消息: {original_system} → {modified_system}")
        return modified_system

    def _extract_province_code_from_system(self, system_message: str) -> Optional[str]:
        """
        从系统消息中提取省份代码
        """
        if not system_message:
            return None

        # 查找"数据库:省份代码"的模式
        pattern = r'数据库\s*[:：]\s*([A-Z]{1,3})'
        match = re.search(pattern, system_message)
        if match:
            province_code = match.group(1).upper()
            return province_code

        return None

    def _get_province_name_by_code(self, province_code: str) -> str:
        """
        根据省份代码获取省份名称

        Args:
            province_code: 省份代码，如 'GZ'

        Returns:
            str: 省份名称，如 '贵州'，如果未找到返回省份代码
        """
        if not province_code:
            return "本省"

        # 从settings获取省份映射
        from src.config import settings
        province_mapping = settings.get_province_database_mapping()
        province_config = province_mapping.get(province_code.upper())

        if province_config and 'prvName' in province_config:
            return province_config['prvName']

        # 如果未找到映射，返回省份代码
        return province_code

    def _map_tool_name(self, tool_name: str) -> str:
        """
        工具名称映射，支持简洁别名

        Args:
            tool_name: 用户输入的工具名称（可能是别名）

        Returns:
            实际的工具名称
        """
        # 工具名称映射表
        tool_mapping = {
            # 知识搜索工具的各种别名
            "知识": "knowledge_search",
            "搜索": "knowledge_search",
            "问答": "knowledge_search",
            "查询": "knowledge_search",
            "kb": "knowledge_search",
            "ask": "knowledge_search",
            "search": "knowledge_search",
            "知识搜索": "knowledge_search",
            "knowledge": "knowledge_search",

            # SQL工具的别名（虽然有专门语法，但保留兼容性）
            "sql": "integrated_sql",
            "数据库": "integrated_sql",
            "SQL": "integrated_sql",

            # 其他可能的工具别名
            "分析": "analysis_tool",
            "计算": "calculation_tool",
            "报告": "report_tool",
        }

        # 查找映射，如果没有找到则返回原名称
        mapped_name = tool_mapping.get(tool_name.lower(), tool_name)

        if mapped_name != tool_name:
            logger.info(f"🔄 [工具映射] {tool_name} → {mapped_name}")

        return mapped_name

    def _merge_sql_steps(self, enhanced_steps: List[str]) -> List[str]:
        """
        合并连续的SQL相关步骤

        将数据库切换和SQL查询合并为一个步骤

        Args:
            enhanced_steps: 增强的步骤列表

        Returns:
            合并后的步骤列表
        """
        merged_steps = []
        i = 0

        while i < len(enhanced_steps):
            current_step = enhanced_steps[i]

            # 检查是否是数据库切换指令
            if current_step.startswith("[内部指令-SQL]") and "切换到数据库: " in current_step:
                db_name = current_step.split("切换到数据库: ")[1]

                # 查看下一步是否是SQL查询
                if i + 1 < len(enhanced_steps):
                    next_step = enhanced_steps[i + 1]
                    if next_step.startswith("调用integrated_sql工具，查询: "):
                        query_content = next_step.replace("调用integrated_sql工具，查询: ", "")

                        # 合并两个步骤
                        merged_step = f"[内部指令-SQL-合并] | 数据库:{db_name} | 查询:{query_content}"
                        merged_steps.append(merged_step)

                        logger.info(f"🔄 [步骤合并] 合并SQL步骤: 切换到{db_name} + 查询{query_content}")

                        # 跳过下一步，因为已经合并了
                        i += 2
                        continue

                # 如果下一步不是查询，保持原样
                merged_steps.append(current_step)
                i += 1
            else:
                # 普通步骤，直接添加
                merged_steps.append(current_step)
                i += 1

        return merged_steps

    def _replace_dynamic_parameters(self, text: str) -> str:
        """
        替换文本中的动态参数

        支持的参数：
        - {本省} - 省份中文名称
        - {省份代码} - 省份代码
        - {数据库} - 当前数据库名称
        - {用户问题} - 用户原始问题

        Args:
            text: 包含动态参数的文本

        Returns:
            替换后的文本
        """
        if not text:
            return text

        # 获取当前省份信息
        current_province_code = self.current_database_override or self.original_province_code or "GZ"
        current_province_name = self._get_province_name_by_code(current_province_code)

        # 执行参数替换
        replaced_text = text

        # 替换省份相关参数
        replaced_text = replaced_text.replace("{本省}", current_province_name)
        replaced_text = replaced_text.replace("{省份代码}", current_province_code)
        replaced_text = replaced_text.replace("{数据库}", current_province_code)

        # 替换用户问题
        if self.current_user_message:
            replaced_text = replaced_text.replace("{用户问题}", self.current_user_message)

        # 记录替换日志
        if replaced_text != text:
            logger.info(f"🔄 [参数替换] '{text}' → '{replaced_text}'")

        return replaced_text
