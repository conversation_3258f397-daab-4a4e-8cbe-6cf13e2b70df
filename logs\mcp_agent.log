2025-08-26 15:36:43,507 - __main__ - INFO - 正在启动MCP Agent服务...
2025-08-26 15:36:49,230 - src.config.mcp_config - INFO - MCP配置已保存到: E:\analysis-agent\src\config\mcp_servers.json
2025-08-26 15:36:49,241 - __main__ - ERROR - 创建MCP Agent API应用失败: 'FieldInfo' object is not iterable
2025-08-26 15:36:49,241 - __main__ - ERROR - 启动MCP Agent服务失败: 'FieldInfo' object is not iterable
2025-08-26 15:38:18,977 - __main__ - INFO - 正在启动MCP Agent服务...
2025-08-26 15:38:21,649 - __main__ - INFO - MCP Agent API应用创建成功
2025-08-26 15:38:42,550 - __main__ - INFO - 正在启动MCP Agent服务...
2025-08-26 15:38:45,066 - __main__ - INFO - MCP Agent API应用创建成功
2025-08-26 15:46:08,619 - __main__ - INFO - 正在启动MCP Agent服务...
2025-08-26 15:46:11,096 - __main__ - INFO - MCP Agent API应用创建成功
2025-08-26 15:47:50,941 - src.api.mcp_api - INFO - MCP Agent初始化成功
2025-08-26 15:47:50,941 - src.api.mcp_api - INFO - 开始搜索: 运营商5G基站
2025-08-26 15:47:53,443 - httpx - INFO - HTTP Request: POST https://mcp.api-inference.modelscope.net/61190dcb5d9246/sse "HTTP/1.1 405 Method Not Allowed"
2025-08-26 15:47:53,444 - src.agents.mcp_agent - ERROR - MCP服务器响应错误: 405
2025-08-26 15:47:53,447 - src.agents.mcp_agent - WARNING - 有效结果不足5条（当前0条），尝试扩大搜索范围
2025-08-26 15:47:54,198 - httpx - INFO - HTTP Request: POST https://mcp.api-inference.modelscope.net/61190dcb5d9246/sse "HTTP/1.1 405 Method Not Allowed"
2025-08-26 15:47:54,199 - src.agents.mcp_agent - ERROR - MCP服务器响应错误: 405
2025-08-26 15:47:54,690 - httpx - INFO - HTTP Request: POST https://mcp.api-inference.modelscope.net/61190dcb5d9246/sse "HTTP/1.1 405 Method Not Allowed"
2025-08-26 15:47:54,690 - src.agents.mcp_agent - ERROR - MCP服务器响应错误: 405
2025-08-26 15:47:55,226 - httpx - INFO - HTTP Request: POST https://mcp.api-inference.modelscope.net/61190dcb5d9246/sse "HTTP/1.1 405 Method Not Allowed"
2025-08-26 15:47:55,227 - src.agents.mcp_agent - ERROR - MCP服务器响应错误: 405
2025-08-26 16:14:36,943 - src.config.mcp_config_manager - INFO - 加载MCP配置成功: src/config/mcp_servers.json
2025-08-26 16:14:36,944 - root - ERROR - 启动失败: 'LLMFactory' object has no attribute 'create_llm'
Traceback (most recent call last):
  File "E:\analysis-agent\start_mcp_agent.py", line 283, in main
    llm = LLMFactory().create_llm()
AttributeError: 'LLMFactory' object has no attribute 'create_llm'
2025-08-26 16:18:02,246 - src.config.mcp_config_manager - INFO - 加载MCP配置成功: src/config/mcp_servers.json
2025-08-26 16:18:02,247 - root - ERROR - 启动失败: 'LLMFactory' object has no attribute 'create_llm'
Traceback (most recent call last):
  File "E:\analysis-agent\start_mcp_agent.py", line 283, in main
    llm = LLMFactory().create_llm()
AttributeError: 'LLMFactory' object has no attribute 'create_llm'
