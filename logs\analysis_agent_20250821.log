2025-08-21 15:04:28,330 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-21 15:04:28,331 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-21 15:04:28,331 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-21 15:04:28,331 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-21 15:04:28,359 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-21 15:04:28,360 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-21 15:04:28,361 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-21 15:04:28,361 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-21 15:04:28,634 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-21 15:04:30,198 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-21 15:04:31,428 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-21 15:04:31,429 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 9
2025-08-21 15:04:31,462 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-21 15:04:31,463 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-21 15:04:31,463 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-21 15:04:31,464 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-21 15:04:31,464 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-21 15:04:31,465 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-21 15:04:31,466 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-21 15:04:31,466 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-21 15:04:31,467 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-21 15:04:31,555 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-21 15:04:31,556 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-21 15:04:31,556 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
