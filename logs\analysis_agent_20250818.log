2025-08-18 10:23:32,154 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:23:32,156 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 10:23:32,157 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 10:23:32,163 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 10:23:33,347 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:23:33,798 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:23:33,800 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:23:33,801 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:27:30,638 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:27:30,640 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:27:30,642 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:27:30,644 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:27:30,963 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:27:31,443 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:27:31,596 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:27:31,598 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:27:31,598 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:31:35,811 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,814 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:31:35,815 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:31:36,109 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:31:36,150 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:31:37,177 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:31:37,180 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,181 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:31:37,182 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:31:37,182 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 10:31:37,392 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:37,393 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,393 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:43,135 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:43,137 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:37:43,473 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:37:43,913 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:37:44,190 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:37:44,191 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:37:44,208 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:37:44,210 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,210 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:44,211 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:37:44,211 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 10:37:44,255 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:44,256 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,257 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:40,043 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,045 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:11:40,695 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:11:41,553 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:11:42,305 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:11:42,306 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:11:42,324 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:11:42,326 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:11:42,327 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,327 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:42,328 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:11:42,328 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:11:42,453 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:42,454 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,455 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:16,107 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,110 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:12:16,111 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:12:16,113 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:12:16,412 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:12:16,850 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:12:17,167 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:12:17,168 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:12:17,187 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:12:17,187 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,188 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:17,189 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:12:17,189 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:12:17,241 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:17,242 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,242 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:37,537 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,540 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:47:37,542 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:47:38,862 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,864 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 11:47:39,339 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:47:39,630 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:47:39,631 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:47:39,648 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:47:39,649 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:47:39,651 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:47:39,651 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,652 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:39,652 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:47:39,653 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:47:39,712 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:39,713 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,713 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:05,498 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:05,501 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:05,503 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:05,504 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:06,969 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,971 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:07,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:07,723 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:07,723 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,724 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:07,725 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:07,725 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:07,774 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:07,775 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,776 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:40,262 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,265 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:41,659 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,661 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:42,157 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:42,439 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:42,442 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,443 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:42,443 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:42,444 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:42,495 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:42,495 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,496 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:39:08,709 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:39:08,712 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:39:10,052 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,054 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,055 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,055 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:39:10,626 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:39:10,909 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:39:10,910 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:39:10,930 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:39:10,931 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:39:10,931 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:39:10,932 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:39:10,933 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:39:10,933 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:39:10,934 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:39:10,935 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:39:10,937 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:39:10,998 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:39:10,999 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:39:11,000 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:48:54,910 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:48:54,912 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 12:48:54,913 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 12:48:54,913 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 12:48:54,914 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 12:48:54,914 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 12:48:54,915 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 12:48:55,445 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:48:55,727 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:48:55,728 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:48:55,747 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:48:55,748 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:48:55,749 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:48:55,750 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:48:55,751 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:48:55,752 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:48:55,752 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:48:55,753 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:48:55,754 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:48:55,809 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:48:55,809 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:48:55,810 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:56:37,691 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:56:37,693 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 12:56:37,694 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 12:56:37,694 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 12:56:37,695 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 12:56:37,695 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 12:56:37,696 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 12:56:38,212 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:56:38,526 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:56:38,526 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:56:38,547 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:56:38,547 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:56:38,548 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:56:38,548 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:56:38,549 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:56:38,550 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:56:38,551 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:56:38,552 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:56:38,555 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:56:38,617 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:56:38,619 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:56:38,621 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:41:27,565 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:41:27,567 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 13:41:27,568 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:41:27,568 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 13:41:27,572 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 13:41:27,573 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:41:27,573 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 13:41:27,574 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 13:41:27,879 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 13:41:28,327 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:41:28,621 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:41:28,621 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:41:28,640 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:41:28,640 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:41:28,641 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:41:28,642 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:41:28,643 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:41:28,643 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:41:28,644 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:41:28,645 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:41:28,645 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 13:41:28,701 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:41:28,702 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:41:28,702 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:44:46,623 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:44:46,625 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 13:44:46,626 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 13:44:46,626 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 13:44:46,627 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 13:44:46,627 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 13:44:46,628 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 13:44:47,138 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:44:47,415 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:44:47,416 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:44:47,440 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:44:47,442 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:44:47,443 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:44:47,443 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:44:47,444 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:44:47,444 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:44:47,445 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:44:47,445 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:44:47,446 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 13:44:47,509 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:44:47,510 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:44:47,511 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:48:29,875 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 13:48:29,879 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 13:48:29,880 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:29,881 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 13:48:29,881 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 13:48:31,767 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,767 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,768 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,769 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 13:48:32,231 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:48:32,510 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:48:32,510 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:48:32,527 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:48:32,528 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:48:32,528 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:48:32,529 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:48:32,529 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:48:32,530 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:48:32,530 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:48:32,531 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:48:32,532 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 13:48:32,584 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:48:32,584 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:48:32,585 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:53:35,052 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:53:49,268 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:54:06,812 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:54:26,557 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:58:07,134 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:58:23,094 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:59:04,478 - langsmith.client - WARNING - Failed to multipart ingest runs: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=c64b0e97-5020-4b38-ac01-b43d949b5676,id=c64b0e97-5020-4b38-ac01-b43d949b5676
2025-08-18 13:59:13,331 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=10)
2025-08-18 13:59:13,692 - langsmith.client - WARNING - Failed to send compressed multipart ingest: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=c64b0e97-5020-4b38-ac01-b43d949b5676,id=c64b0e97-5020-4b38-ac01-b43d949b5676
2025-08-18 14:00:00,356 - langsmith.client - WARNING - Failed to multipart ingest runs: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=90f417e4-7a57-42fc-bd14-58cf4bef31c5,id=90f417e4-7a57-42fc-bd14-58cf4bef31c5
2025-08-18 14:00:09,686 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='ai.ai.iot.chinamobile.com', port=80): Read timed out. (read timeout=10)
2025-08-18 14:00:09,997 - langsmith.client - WARNING - Failed to send compressed multipart ingest: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=90f417e4-7a57-42fc-bd14-58cf4bef31c5,id=90f417e4-7a57-42fc-bd14-58cf4bef31c5
2025-08-18 14:02:14,312 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:02:14,313 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 14:02:14,313 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:02:14,314 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 14:02:14,315 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 14:02:14,316 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:02:14,316 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 14:02:14,317 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 14:02:14,641 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 14:02:15,145 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 14:02:15,396 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 14:02:15,396 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 14:02:15,416 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 14:02:15,417 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 14:02:15,418 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 14:02:15,418 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 14:02:15,419 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 14:02:15,419 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:02:15,420 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:02:15,420 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 14:02:15,421 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 14:02:15,476 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:02:15,477 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:02:15,478 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:03:20,558 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='proxy.ai.iot.chinamobile.com', port=80): Max retries exceeded with url: /imaas/v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000002692DA78460>: Failed to resolve 'proxy.ai.iot.chinamobile.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-18 14:03:43,729 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:03:43,730 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 14:03:43,731 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:03:43,731 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 14:03:43,732 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 14:03:43,996 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 14:03:44,445 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 14:03:44,700 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 14:03:44,700 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 14:03:44,719 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 14:03:44,719 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 14:03:44,720 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 14:03:44,720 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 14:03:44,721 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 14:03:44,721 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:03:44,722 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:03:44,723 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 14:03:44,723 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 14:03:44,770 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:03:44,771 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:03:44,771 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:15:19,182 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:15:19,182 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:15:19,183 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:19,183 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:15:19,185 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:15:19,185 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:19,186 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:15:19,186 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:15:19,499 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:15:20,322 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:20,323 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:20,326 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:49,982 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:15:49,983 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:15:49,984 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:49,984 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:15:49,986 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:15:49,986 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:49,987 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:15:49,987 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:15:50,277 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:15:50,672 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:50,673 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:50,675 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:06,302 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:16:06,302 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:16:06,303 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:16:06,303 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:16:06,304 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:16:06,305 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:16:06,305 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:16:06,306 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:16:06,616 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:16:07,051 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:07,052 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:07,054 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,021 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:19:07,022 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:19:07,023 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:19:07,023 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:19:07,027 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:19:07,028 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:19:07,028 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:19:07,029 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:19:07,353 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:19:07,728 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,729 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,732 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:30:26,331 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:30:26,333 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:30:26,334 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:30:26,334 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:30:26,336 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:30:26,336 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:30:26,337 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:30:26,337 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:30:26,629 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:30:27,008 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:30:27,009 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:34:51,873 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:34:51,876 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:34:51,876 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:34:51,877 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:34:51,878 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:34:51,879 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:34:51,879 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:34:51,880 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:34:52,187 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:34:52,524 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
2025-08-18 17:34:52,525 - __main__ - ERROR - ❌ SQL Agent初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
2025-08-18 17:35:47,600 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:35:47,603 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:35:47,604 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:35:47,604 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:35:47,606 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:35:47,607 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:35:47,607 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:35:47,608 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:35:47,923 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:35:48,568 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:35:48,569 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:36:13,690 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:36:13,692 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:36:13,692 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:36:13,693 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:36:13,695 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:36:13,695 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:36:13,696 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:36:13,697 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:36:14,554 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:36:15,182 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 17:36:16,013 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 17:36:16,013 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 17:36:16,059 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 17:36:16,060 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 17:36:16,061 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 17:36:16,062 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 17:36:16,062 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 17:36:16,063 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:36:16,064 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:36:16,064 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:36:16,065 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:36:16,065 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:36:16,066 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 17:36:16,067 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 17:36:16,259 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:36:16,260 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:36:16,261 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:36:16,261 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:36:16,262 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:36:16,263 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:37:20,203 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:37:20,207 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:37:20,207 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:37:20,209 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:37:20,210 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:37:20,210 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:37:20,211 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:37:20,212 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:37:20,513 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:37:20,971 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:37:20,972 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:39:22,904 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:39:22,906 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:39:22,907 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:39:22,908 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:39:22,910 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:39:22,911 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:39:22,912 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:39:22,913 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:39:23,570 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:39:24,017 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
2025-08-18 17:39:24,018 - __main__ - ERROR - ❌ SQL Agent初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
2025-08-18 17:39:48,860 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:39:48,863 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:39:48,863 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:39:48,864 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:39:48,865 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:39:48,866 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:39:48,866 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:39:48,866 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:39:49,172 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:39:49,839 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 17:39:50,137 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 17:39:50,138 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 17:39:50,156 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 17:39:50,157 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 17:39:50,158 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 17:39:50,158 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 17:39:50,159 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 17:39:50,159 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:39:50,160 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:39:50,160 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:39:50,161 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:39:50,162 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:39:50,162 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 17:39:50,163 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 17:39:50,211 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:39:50,212 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:39:50,212 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:39:50,213 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:39:50,213 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:39:50,214 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:41:27,057 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:41:27,059 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:41:27,059 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:41:27,060 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:41:27,061 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:41:27,061 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:41:27,062 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:41:27,063 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:41:27,356 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:41:27,804 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 17:41:28,063 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 17:41:28,063 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 17:41:28,083 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 17:41:28,083 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 17:41:28,084 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 17:41:28,084 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 17:41:28,085 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 17:41:28,085 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:41:28,085 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:41:28,086 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:41:28,086 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:41:28,087 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:41:28,088 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 17:41:28,088 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 17:41:28,139 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:41:28,140 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:41:28,141 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:41:28,141 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:41:28,142 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:41:28,142 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:43:28,374 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:43:28,377 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:43:28,378 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:43:28,379 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:43:28,380 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:43:28,380 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:43:28,381 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:43:28,382 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:43:28,666 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:43:29,164 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 17:43:29,447 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 17:43:29,448 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 17:43:29,469 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 17:43:29,470 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 17:43:29,470 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 17:43:29,471 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 17:43:29,471 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 17:43:29,472 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:43:29,472 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:43:29,473 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:43:29,473 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:43:29,474 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:43:29,475 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 17:43:29,475 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 17:43:29,527 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:43:29,529 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:43:29,529 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:43:29,530 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:43:29,531 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:43:29,531 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:56:00,538 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:56:00,540 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:56:00,541 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:56:00,541 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:56:00,543 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:56:00,543 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:56:00,544 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:56:00,544 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:56:00,862 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:56:01,374 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 17:56:01,646 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 17:56:01,647 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 17:56:01,666 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 17:56:01,666 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 17:56:01,667 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 17:56:01,668 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 17:56:01,668 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 17:56:01,669 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:56:01,669 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:56:01,670 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:56:01,670 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:56:01,671 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:56:01,671 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 17:56:01,672 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 17:56:01,721 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:56:01,722 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-18 17:56:01,722 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-18 17:56:01,723 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-18 17:56:01,723 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 17:56:01,724 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:56:43,490 - __main__ - INFO - 🚀 [API调用] /v1/chat/completions 端点被调用
2025-08-18 17:56:43,496 - __main__ - INFO - 👤 [用户输入] '广西网络电费7月风险'
2025-08-18 17:56:43,497 - __main__ - INFO - 📚 [对话历史] 0 条历史记录
2025-08-18 17:56:43,498 - __main__ - INFO - 🔧 [最终消息] Agent将处理: '广西网络电费7月风险'
2025-08-18 17:56:43,499 - src.agents.analysis_agent_v2 - INFO - 🤖 [AnalysisAgentV2] 开始处理用户消息: 广西网络电费7月风险
2025-08-18 17:56:43,500 - src.agents.vector_enhanced_agent - INFO - 🎯 [问题提取] 未找到特定格式，使用原始消息: 广西网络电费7月风险
2025-08-18 17:56:43,500 - src.agents.vector_enhanced_agent - INFO - 🔍 [向量搜索] 搜索查询: 广西网络电费7月风险
2025-08-18 17:56:43,502 - src.core.prompt_vectorstore_manager - INFO - 🔍 开始搜索复杂提示词，数据库中共有 8 条数据
2025-08-18 17:56:43,503 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索查询: '广西网络电费7月风险', k=3, score_threshold=0.95
2025-08-18 17:56:43,869 - src.core.prompt_vectorstore_manager - INFO - 🔍 向量搜索返回 8 个原始结果
2025-08-18 17:56:43,869 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索到 0 个匹配的复杂提示词
2025-08-18 17:56:43,870 - src.agents.vector_enhanced_agent - INFO - ❌ [向量搜索] 未找到匹配的复杂提示词
2025-08-18 17:56:43,870 - src.agents.components.intent_analyzer - INFO - 🔍 [意图分析] 开始分析用户输入: 广西网络电费7月风险
2025-08-18 17:56:43,871 - src.agents.components.intent_analyzer - INFO - 🎯 [意图分析] 提取的用户问题: 广西网络电费7月风险
2025-08-18 17:56:43,872 - src.agents.components.intent_analyzer - INFO - ✅ [意图分析] 分析完成: complex_analysis, 置信度: 0.900, 多步骤: True
2025-08-18 17:56:43,873 - src.agents.analysis_agent_v2 - INFO - 🎯 [AnalysisAgentV2] 意图分析完成 (用时: 0.37秒)
2025-08-18 17:56:43,874 - src.agents.components.tool_orchestrator - INFO - 📋 [工具编排] 开始规划执行计划，意图类型: complex_analysis
2025-08-18 17:56:43,874 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 执行计划完成，共 5 个步骤
2025-08-18 17:56:43,875 - src.agents.components.tool_orchestrator - INFO - 🚀 [工具编排] 开始执行计划，共 5 个步骤
2025-08-18 17:56:43,875 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 1/5: 查询总体风险情况
2025-08-18 17:56:43,876 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-18 17:56:43,876 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月网络电费风险情况'
2025-08-18 17:56:43,877 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-18 17:56:43,877 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-18 17:56:43,878 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月网络电费风险情况'
2025-08-18 17:56:43,878 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-18 17:56:43,879 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月网络电费风险情况
2025-08-18 17:56:45,092 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 1.22秒)
2025-08-18 17:56:45,093 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-18 17:56:45,094 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 T...
2025-08-18 17:56:45,095 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-18 17:56:45,096 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-18 17:56:45,096 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 1.22秒)
2025-08-18 17:56:45,097 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 THEN '同一订单不同月份机房类型不一致'
        WHEN 6 THEN '同一订单不同月份配套类型不一致'
        WHEN 7 THEN '订单挂高大于塔高'
        WHEN 8 THEN '同一物理站址多次计取维护费'
        WHEN 9 THEN '资源异常预警工单超时'
        WHEN 10 THEN '代维按次工单费用为空'
        WHEN 11 THEN '代维按次工单派单资源不在综资系统中存在'
        WHEN 12 THEN '共享数量与分摊比例不匹配'
        WHEN 13 THEN '已报账缴费单超过合同约定单价'
        WHEN 14 THEN '转改直期间直供电按转供电缴费'
        WHEN 15 THEN '已报账缴费单关联资源退网'
        ELSE '未知类型'
    END AS '指标',
    check_result AS '条数',
    ROUND(involve_amount/10000,2) AS '金额(万元)'
FROM rpt_risk_board 
WHERE rpt_month = '2025-07'
and risk_type in (1,2,13)
2025-08-18 17:56:45,098 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-18 17:56:45,099 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-18 17:56:45,099 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 THEN '同一订单不同月份机房类型不一致'
        WHEN 6 THEN '同一订单不同月份配套类型不一致'
        WHEN 7 THEN '订单挂高大于塔高'
        WHEN 8 THEN '同一物理站址多次计取维护费'
        WHEN 9 THEN '资源异常预警工单超时'
        WHEN 10 THEN '代维按次工单费用为空'
        WHEN 11 THEN '代维按次工单派单资源不在综资系统中存在'
        WHEN 12 THEN '共享数量与分摊比例不匹配'
        WHEN 13 THEN '已报账缴费单超过合同约定单价'
        WHEN 14 THEN '转改直期间直供电按转供电缴费'
        WHEN 15 THEN '已报账缴费单关联资源退网'
        ELSE '未知类型'
    END AS '指标',
    check_result AS '条数',
    ROUND(involve_amount/10000,2) AS '金额(万元)'
FROM rpt_risk_board 
WHERE rpt_month = '2025-07'
and risk_type in (1,2,13)
2025-08-18 17:56:45,141 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 3 条记录
2025-08-18 17:56:45,142 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 1 执行成功
2025-08-18 17:56:45,142 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 2/5: 查询预付费超期未核销详情
2025-08-18 17:56:45,143 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-18 17:56:45,144 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标预付费超期未核销情况'
2025-08-18 17:56:45,145 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-18 17:56:45,146 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-18 17:56:45,146 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标预付费超期未核销情况'
2025-08-18 17:56:45,147 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-18 17:56:45,148 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标预付费超期未核销情况
2025-08-18 17:56:46,598 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 1.45秒)
2025-08-18 17:56:46,598 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-18 17:56:46,599 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1...
2025-08-18 17:56:46,599 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-18 17:56:46,600 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-18 17:56:46,600 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 1.46秒)
2025-08-18 17:56:46,601 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1 THEN '普通预付费'
		WHEN 2 THEN 'IC卡预付费'
		WHEN 3 THEN '先款后票预付费'
	END AS '预付费类型',
	CASE
		IFNULL(el.billaccount_type, eb.billaccount_type) WHEN 1 THEN '自维电费报账点'
		WHEN 2 THEN '铁塔电费报账点'
		WHEN 3 THEN '代持电费报账点'
	END AS '报账点类型',
	el.billaccount_name as '报账点名称',
	el.contract_code as '合同编码',
	el.contract_name as '合同名称',
	el.loan_date as '借款申请日期',
	el.loan_money as '借款金额',
	evl.verification_amount as '已核销金额'
FROM
	clean_ele_loan el
LEFT JOIN ele_loan_billamount elb ON
	el.billamount_id = elb.billamount_id
LEFT JOIN ele_loan_balance e ON
	el.billaccount_id = e.billaccount_id
left join clean_ele_billaccount eb on
	el.billaccount_id = eb.billaccount_id
LEFT JOIN (
	SELECT
		SUM(verification_amount) verification_amount,
		loan_id
	from
		clean_ele_verification_loan
	GROUP BY
		loan_id) evl on
	el.loan_id = evl.loan_id
where
	case
		e.type WHEN 1 THEN loan_money - IFNULL(verification_amount, 0) > 0
		and el.loan_date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
		else loan_money - IFNULL(verification_amount, 0) > 0
		AND elb.finance_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
	END
	AND '2025-08-01' > el.loan_date
	and el.loan_state != -1
	AND el.is_finance = 1
	and el.data_state = 0
	AND elb.billamount_state = 2
	limit 10
2025-08-18 17:56:46,604 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-18 17:56:46,604 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-18 17:56:46,605 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1 THEN '普通预付费'
		WHEN 2 THEN 'IC卡预付费'
		WHEN 3 THEN '先款后票预付费'
	END AS '预付费类型',
	CASE
		IFNULL(el.billaccount_type, eb.billaccount_type) WHEN 1 THEN '自维电费报账点'
		WHEN 2 THEN '铁塔电费报账点'
		WHEN 3 THEN '代持电费报账点'
	END AS '报账点类型',
	el.billaccount_name as '报账点名称',
	el.contract_code as '合同编码',
	el.contract_name as '合同名称',
	el.loan_date as '借款申请日期',
	el.loan_money as '借款金额',
	evl.verification_amount as '已核销金额'
FROM
	clean_ele_loan el
LEFT JOIN ele_loan_billamount elb ON
	el.billamount_id = elb.billamount_id
LEFT JOIN ele_loan_balance e ON
	el.billaccount_id = e.billaccount_id
left join clean_ele_billaccount eb on
	el.billaccount_id = eb.billaccount_id
LEFT JOIN (
	SELECT
		SUM(verification_amount) verification_amount,
		loan_id
	from
		clean_ele_verification_loan
	GROUP BY
		loan_id) evl on
	el.loan_id = evl.loan_id
where
	case
		e.type WHEN 1 THEN loan_money - IFNULL(verification_amount, 0) > 0
		and el.loan_date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
		else loan_money - IFNULL(verification_amount, 0) > 0
		AND elb.finance_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
	END
	AND '2025-08-01' > el.loan_date
	and el.loan_state != -1
	AND el.is_finance = 1
	and el.data_state = 0
	AND elb.billamount_state = 2
	limit 10
2025-08-18 17:56:46,680 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 10 条记录
2025-08-18 17:56:46,681 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 2 执行成功
2025-08-18 17:56:46,681 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 3/5: 查询转供电非正规发票详情
2025-08-18 17:56:46,682 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-18 17:56:46,683 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标转供电非正规发票情况'
2025-08-18 17:56:46,683 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-18 17:56:46,684 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-18 17:56:46,684 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标转供电非正规发票情况'
2025-08-18 17:56:46,685 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-18 17:56:46,686 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标转供电非正规发票情况
2025-08-18 17:56:47,598 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 0.91秒)
2025-08-18 17:56:47,598 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-18 17:56:47,599 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE ...
2025-08-18 17:56:47,600 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-18 17:56:47,600 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-18 17:56:47,601 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 0.92秒)
2025-08-18 17:56:47,601 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE '未知'
	END AS '供电类型',
	CASE 
		WHEN am.second_fee_type = 1 THEN '自维'
		WHEN am.second_fee_type = 2 THEN '代持'
		WHEN am.second_fee_type = 3 THEN '塔维'
		ELSE '未知'
	END AS '报账点类型',
	am.preg_name as '地市',
	am.reg_name as '区县',
	eb.billaccount_code as '报账点编码',
	eb.billaccount_name as '报账点名称',
	CASE 
		WHEN am.site_type = 1 THEN '核心机楼'
		WHEN am.site_type = 2 THEN '汇聚传输站点'
		WHEN am.site_type = 3 THEN '基站'
		WHEN am.site_type = 4 THEN '室分及WLAN'
		WHEN am.site_type = 5 THEN '家客集客'
		WHEN am.site_type = 6 THEN 'IDC机房'
		WHEN am.site_type = 7 THEN '基地'
		WHEN am.site_type = 8 THEN '其他'
		WHEN am.site_type = 9 THEN '传输位置点'
		WHEN am.site_type = 10 THEN '综合位置点'
		WHEN am.site_type IS NULL THEN '-'
		ELSE '未知'
	END AS '站点类型',
	am.payment_code as '缴费单编码',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '增值税专票'
		WHEN am.invoice_type = 22 THEN '增值税专票'
		WHEN am.invoice_type = 23 THEN '增值税专票'
		WHEN am.invoice_type = 24 THEN '增值税专票'
		WHEN am.invoice_type = 25 THEN '增值税专票'
		WHEN am.invoice_type = 29 THEN '增值税专票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '增值税普票'
		WHEN am.invoice_type = 31 THEN '增值税普票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据'
		WHEN am.invoice_type = 27 THEN '收据'
		WHEN am.invoice_type = 30 THEN '收据'
		WHEN am.invoice_type = 28 THEN '收据'
		ELSE '未知'
	END AS '票据类型归类',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '16%增值税专用发票'
		WHEN am.invoice_type = 22 THEN '17%增值税专用发票'
		WHEN am.invoice_type = 23 THEN '3%增值税专用发票'
		WHEN am.invoice_type = 24 THEN '6%增值税专用发票'
		WHEN am.invoice_type = 25 THEN '13%增值税专用发票'
		WHEN am.invoice_type = 29 THEN '增值税专用发票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '发票'
		WHEN am.invoice_type = 31 THEN '普通发票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据+发票复印件+分割单'
		WHEN am.invoice_type = 27 THEN '发票复印件+分割单'
		WHEN am.invoice_type = 30 THEN '收据/白条'
		WHEN am.invoice_type = 28 THEN '同缴费票据类型'
		ELSE '未知'
	END AS '缴费单票据类型(三费)',
	round(am.bill_amount_actual, 2) as '实际报账金额(元)'
FROM
	aggr_ele_amortize_info am
INNER JOIN clean_ele_billaccount eb ON
	eb.billaccount_id = am.billaccount_id
WHERE
	YEAR(am.billamount_startdate) = 2025
	AND MONTH(am.billamount_startdate) = 7
	AND am.supply_mothed = 2
	AND am.invoice_type IN (11, 13, 27, 30)
2025-08-18 17:56:47,605 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-18 17:56:47,606 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-18 17:56:47,606 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE '未知'
	END AS '供电类型',
	CASE 
		WHEN am.second_fee_type = 1 THEN '自维'
		WHEN am.second_fee_type = 2 THEN '代持'
		WHEN am.second_fee_type = 3 THEN '塔维'
		ELSE '未知'
	END AS '报账点类型',
	am.preg_name as '地市',
	am.reg_name as '区县',
	eb.billaccount_code as '报账点编码',
	eb.billaccount_name as '报账点名称',
	CASE 
		WHEN am.site_type = 1 THEN '核心机楼'
		WHEN am.site_type = 2 THEN '汇聚传输站点'
		WHEN am.site_type = 3 THEN '基站'
		WHEN am.site_type = 4 THEN '室分及WLAN'
		WHEN am.site_type = 5 THEN '家客集客'
		WHEN am.site_type = 6 THEN 'IDC机房'
		WHEN am.site_type = 7 THEN '基地'
		WHEN am.site_type = 8 THEN '其他'
		WHEN am.site_type = 9 THEN '传输位置点'
		WHEN am.site_type = 10 THEN '综合位置点'
		WHEN am.site_type IS NULL THEN '-'
		ELSE '未知'
	END AS '站点类型',
	am.payment_code as '缴费单编码',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '增值税专票'
		WHEN am.invoice_type = 22 THEN '增值税专票'
		WHEN am.invoice_type = 23 THEN '增值税专票'
		WHEN am.invoice_type = 24 THEN '增值税专票'
		WHEN am.invoice_type = 25 THEN '增值税专票'
		WHEN am.invoice_type = 29 THEN '增值税专票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '增值税普票'
		WHEN am.invoice_type = 31 THEN '增值税普票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据'
		WHEN am.invoice_type = 27 THEN '收据'
		WHEN am.invoice_type = 30 THEN '收据'
		WHEN am.invoice_type = 28 THEN '收据'
		ELSE '未知'
	END AS '票据类型归类',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '16%增值税专用发票'
		WHEN am.invoice_type = 22 THEN '17%增值税专用发票'
		WHEN am.invoice_type = 23 THEN '3%增值税专用发票'
		WHEN am.invoice_type = 24 THEN '6%增值税专用发票'
		WHEN am.invoice_type = 25 THEN '13%增值税专用发票'
		WHEN am.invoice_type = 29 THEN '增值税专用发票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '发票'
		WHEN am.invoice_type = 31 THEN '普通发票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据+发票复印件+分割单'
		WHEN am.invoice_type = 27 THEN '发票复印件+分割单'
		WHEN am.invoice_type = 30 THEN '收据/白条'
		WHEN am.invoice_type = 28 THEN '同缴费票据类型'
		ELSE '未知'
	END AS '缴费单票据类型(三费)',
	round(am.bill_amount_actual, 2) as '实际报账金额(元)'
FROM
	aggr_ele_amortize_info am
INNER JOIN clean_ele_billaccount eb ON
	eb.billaccount_id = am.billaccount_id
WHERE
	YEAR(am.billamount_startdate) = 2025
	AND MONTH(am.billamount_startdate) = 7
	AND am.supply_mothed = 2
	AND am.invoice_type IN (11, 13, 27, 30)
2025-08-18 17:56:47,656 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 4 条记录
2025-08-18 17:56:47,657 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 3 执行成功
2025-08-18 17:56:47,657 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 4/5: 查询已报账缴费单超过合同约定单价详情
2025-08-18 17:56:47,658 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-18 17:56:47,658 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标已报账缴费单超过合同约定单价情况'
2025-08-18 17:56:47,658 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-18 17:56:47,659 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-18 17:56:47,659 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标已报账缴费单超过合同约定单价情况'
2025-08-18 17:56:47,659 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-18 17:56:47,660 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标已报账缴费单超过合同约定单价情况
2025-08-18 17:56:48,806 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 1.15秒)
2025-08-18 17:56:48,806 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-18 17:56:48,807 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHE...
2025-08-18 17:56:48,811 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-18 17:56:48,812 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-18 17:56:48,812 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 1.15秒)
2025-08-18 17:56:48,813 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHEN 2 THEN '财务已审核'
        WHEN 8 THEN '财务退回'
        ELSE '-'
    END AS '推送状态',
    t.billamount_code as '汇总单编码',
    t.preg_name as '所属地市',
    t.reg_name as '所属区县',
    t.billaccount_code as '报账点编码',
    t.payment_code as '缴费单编码',
    t.payment_days as '缴费单缴费天数',
    t.cover_days as '当月缴费天数',
    t.price_actual as '电费不含税金额',
    t.total_degree_actual as '用电量',
    t.payment_price as '缴费单电价',
    t.max_contract_price as '合同电价（最高）'
FROM
    (
        SELECT
            eba.billamount_state,
            eba.billamount_code,
            ceb.preg_name,
            ceb.reg_name,
            ceb.billaccount_code,
            ep.payment_code,
            ( ep.price_actual / ep.total_degree_actual ) AS payment_price,
            IF
                (
                        cec.price_type = 0,
                        GREATEST(
                                SUBSTRING_INDEX( cec.elecontract_price, '|', 1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 2 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 3 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 4 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 5 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 6 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 7 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 8 ), '|',-1 )
                            ),
                        GREATEST( cec.flat_price, cec.peak_price, cec.valley_price, cec.top_price )
                ) AS max_contract_price,
            ep.bill_amount_actual,
            (DATEDIFF(ep.billamount_enddate,ep.billamount_startdate)+1) AS payment_days,
            (DATEDIFF(
                     IF(ep.billamount_enddate > DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),ep.billamount_enddate),
                     IF(ep.billamount_startdate > CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),ep.billamount_startdate,CONCAT(2025,'-', LPAD(7, 2, 0),'-01')))
                +1)AS cover_days,
            ep.price_actual,
            ep.total_degree_actual
        FROM
            clean_ele_payment AS ep
                INNER JOIN clean_ele_contract AS cec ON cec.elecontract_id = ep.elecontract_id
                INNER JOIN ele_billamount AS eba ON eba.billamount_id = ep.billamount_id
                INNER JOIN clean_ele_billaccount AS ceb ON ceb.billaccount_id = ep.billaccount_id
        WHERE
            cec.is_include_all = 0
          AND eba.billamount_state = 2
          AND CONCAT(2025,'-', LPAD(7, 2, 0)) BETWEEN DATE_FORMAT(ep.billamount_startdate , '%Y-%m') AND DATE_FORMAT(ep.billamount_enddate , '%Y-%m')
        GROUP BY
            ep.payment_code
    ) AS t
WHERE
    t.payment_price > t.max_contract_price
2025-08-18 17:56:48,822 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-18 17:56:48,833 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-18 17:56:48,833 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHEN 2 THEN '财务已审核'
        WHEN 8 THEN '财务退回'
        ELSE '-'
    END AS '推送状态',
    t.billamount_code as '汇总单编码',
    t.preg_name as '所属地市',
    t.reg_name as '所属区县',
    t.billaccount_code as '报账点编码',
    t.payment_code as '缴费单编码',
    t.payment_days as '缴费单缴费天数',
    t.cover_days as '当月缴费天数',
    t.price_actual as '电费不含税金额',
    t.total_degree_actual as '用电量',
    t.payment_price as '缴费单电价',
    t.max_contract_price as '合同电价（最高）'
FROM
    (
        SELECT
            eba.billamount_state,
            eba.billamount_code,
            ceb.preg_name,
            ceb.reg_name,
            ceb.billaccount_code,
            ep.payment_code,
            ( ep.price_actual / ep.total_degree_actual ) AS payment_price,
            IF
                (
                        cec.price_type = 0,
                        GREATEST(
                                SUBSTRING_INDEX( cec.elecontract_price, '|', 1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 2 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 3 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 4 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 5 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 6 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 7 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 8 ), '|',-1 )
                            ),
                        GREATEST( cec.flat_price, cec.peak_price, cec.valley_price, cec.top_price )
                ) AS max_contract_price,
            ep.bill_amount_actual,
            (DATEDIFF(ep.billamount_enddate,ep.billamount_startdate)+1) AS payment_days,
            (DATEDIFF(
                     IF(ep.billamount_enddate > DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),ep.billamount_enddate),
                     IF(ep.billamount_startdate > CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),ep.billamount_startdate,CONCAT(2025,'-', LPAD(7, 2, 0),'-01')))
                +1)AS cover_days,
            ep.price_actual,
            ep.total_degree_actual
        FROM
            clean_ele_payment AS ep
                INNER JOIN clean_ele_contract AS cec ON cec.elecontract_id = ep.elecontract_id
                INNER JOIN ele_billamount AS eba ON eba.billamount_id = ep.billamount_id
                INNER JOIN clean_ele_billaccount AS ceb ON ceb.billaccount_id = ep.billaccount_id
        WHERE
            cec.is_include_all = 0
          AND eba.billamount_state = 2
          AND CONCAT(2025,'-', LPAD(7, 2, 0)) BETWEEN DATE_FORMAT(ep.billamount_startdate , '%Y-%m') AND DATE_FORMAT(ep.billamount_enddate , '%Y-%m')
        GROUP BY
            ep.payment_code
    ) AS t
WHERE
    t.payment_price > t.max_contract_price
2025-08-18 17:56:48,914 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 4 条记录
2025-08-18 17:56:48,914 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 4 执行成功
2025-08-18 17:56:48,915 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 5/5: 搜索风险管理改进建议
2025-08-18 17:56:48,915 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] 🔍 正在查询知识库: 网络电费风险管理 改进建议
2025-08-18 17:56:48,916 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] API地址: http://10.12.22.20:8081/v1/workflows/run
2025-08-18 17:56:48,916 - src.tools.knowledge_search_tools - INFO - 🌐 [知识库] 发送API请求...
2025-08-18 17:56:48,917 - src.tools.knowledge_search_tools - INFO - 📤 [知识库] 请求体: {'inputs': {'query': '网络电费风险管理 改进建议'}, 'response_mode': 'blocking', 'user': 'sql-agent'}
2025-08-18 17:56:53,444 - src.tools.knowledge_search_tools - INFO - 🌐 [知识库] API响应状态码: 200 (请求用时: 4.53秒)
2025-08-18 17:56:53,445 - src.tools.knowledge_search_tools - INFO - 📥 [知识库] API返回数据结构: ['task_id', 'workflow_run_id', 'data']
2025-08-18 17:56:53,447 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] ✅ 知识库查询成功，找到 4 条相关信息 (总用时: 4.53秒，请求: 4.53秒，解析: 0.001秒，格式化: 0.000秒)
2025-08-18 17:56:53,447 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 标题: 集团三费系统常见问题答疑.xlsx
2025-08-18 17:56:53,448 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 相关度: 1.0
2025-08-18 17:56:53,448 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 内容预览: 系统常见公式":"发票税额=调整后电费税额（实际推送电费税额）+其他费用税金汇总金额"
2025-08-18 17:56:53,449 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 标题: 网络成本管理系统使用手册(电费录入、报账人员部分)20241106.docx
2025-08-18 17:56:53,449 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 相关度: 1.0
2025-08-18 17:56:53,450 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 内容预览: 除以上不含税金额计算的用电成本提示以外，网络电费还可配置用电成本稽核流程，在标杆参数配置页面增加需要省管理员稽核的用电成本上限后，系统将在审核流程中判断实际用电成本是否超出限制，超过限制的缴费单在摘要...
2025-08-18 17:56:53,450 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 标题: 费控助手指标计算逻辑.md
2025-08-18 17:56:53,451 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 相关度: 1.0
2025-08-18 17:56:53,452 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 内容预览: 指标名：用电成本
别名：单位用电成本 
计算逻辑：用电成本=电费/电量
2025-08-18 17:56:53,453 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 5 执行成功
2025-08-18 17:56:53,453 - src.agents.components.tool_orchestrator - INFO - 🏁 [工具编排] 执行完成，成功 5/5 个步骤
2025-08-18 17:56:53,454 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] 工具编排完成 (用时: 9.58秒)
2025-08-18 17:56:53,454 - src.agents.components.response_formatter - INFO - 📝 [结果整合] 开始格式化响应，工具结果数: 5
2025-08-18 17:56:53,455 - src.agents.components.response_formatter - INFO - ✅ [结果整合] 响应格式化完成，包含表格: True
2025-08-18 17:56:53,456 - src.agents.analysis_agent_v2 - INFO - 📝 [AnalysisAgentV2] 结果整合完成 (用时: 0.00秒)
2025-08-18 17:56:53,457 - src.agents.analysis_agent_v2 - INFO - ✅ [AnalysisAgentV2] 处理完成 (总用时: 9.96秒)
